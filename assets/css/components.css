/* To create custom classes */
@layer components {
    /* Şölen Homepage Specific Styles */
    .solen-hero-banner .swiper-button-next,
    .solen-hero-banner .swiper-button-prev {
        @apply h-12 w-12 rounded-full bg-white bg-opacity-80 text-gray-800 shadow-lg transition-all duration-300 hover:bg-opacity-100 hover:scale-110;
    }

    .solen-hero-banner .swiper-button-next {
        @apply right-4;
    }

    .solen-hero-banner .swiper-button-prev {
        @apply left-4;
    }

    .solen-brand-showcase .brand-item {
        @apply transition-all duration-300;
    }

    .solen-brand-showcase .brand-item:hover {
        @apply transform scale-105;
    }

    .product-floating-slider .swiper-slide {
        @apply flex items-center justify-center;
    }

    .product-floating-slider .swiper-slide img {
        @apply transition-transform duration-500 hover:scale-110;
        animation: float 6s ease-in-out infinite;
    }

    .product-floating-slider .swiper-slide:nth-child(2n) img {
        animation-delay: -2s;
    }

    .product-floating-slider .swiper-slide:nth-child(3n) img {
        animation-delay: -4s;
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    .solen-video-section {
        position: relative;
        overflow: hidden;
    }

    .solen-video-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(251, 146, 60, 0.1) 0%, rgba(249, 115, 22, 0.1) 100%);
        pointer-events: none;
    }

    .solen-info-cards .info-card-transition {
        @apply transition-all duration-500 ease-in-out;
    }

    .news-item {
        @apply transition-all duration-300;
    }

    .news-item:hover {
        @apply transform -translate-y-1;
    }

    .blog-post {
        @apply transition-all duration-300;
    }

    .blog-post:hover {
        @apply transform -translate-y-1;
    }

    .boutique-card,
    .catalog-card {
        @apply transition-all duration-300;
    }

    .boutique-card:hover,
    .catalog-card:hover {
        @apply transform -translate-y-2;
    }

    /* Responsive adjustments for Şölen sections */
    @media (max-width: 768px) {
        .solen-brand-showcase h1 {
            @apply text-3xl;
        }

        .solen-brand-showcase h2 {
            @apply text-xl;
        }

        .product-floating-slider {
            @apply hidden;
        }

        .solen-video-section .container {
            @apply px-2;
        }
    }

    /* Custom scrollbar for product sliders */
    .product-floating-slider::-webkit-scrollbar {
        @apply hidden;
    }

    .product-floating-slider {
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    /* Şölen brand colors and typography */
    .solen-orange {
        color: #ff6b35;
    }

    .solen-bg-orange {
        background-color: #ff6b35;
    }

    .solen-text-shadow {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Enhanced hover effects */
    .solen-hover-lift {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .solen-hover-lift:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    /* Gradient backgrounds for sections */
    .solen-gradient-bg {
        background: linear-gradient(135deg, #fff5f0 0%, #ffe8d6 100%);
    }

    .solen-gradient-blue {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    }

    /* Animation for floating elements */
    .solen-float-animation {
        animation: solenFloat 4s ease-in-out infinite;
    }

    @keyframes solenFloat {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
        }
        25% {
            transform: translateY(-5px) rotate(1deg);
        }
        50% {
            transform: translateY(-10px) rotate(0deg);
        }
        75% {
            transform: translateY(-5px) rotate(-1deg);
        }
    }

    /* Pulse animation for call-to-action buttons */
    .solen-pulse {
        animation: solenPulse 2s infinite;
    }

    @keyframes solenPulse {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(255, 107, 53, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(255, 107, 53, 0);
        }
    }
}
