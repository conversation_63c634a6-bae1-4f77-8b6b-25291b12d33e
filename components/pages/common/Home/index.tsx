import {memo} from 'react';
import {Page, SpecialPageProducts} from '@core/types';
import {isDev, trim} from '@core/helpers';
import Seo from '@components/common/Seo';
import SolenHeroBanner from '@components/common/SolenHeroBanner';
import SolenBrandShowcase from './components/SolenBrandShowcase';
import SolenVideoSection from './components/SolenVideoSection';
import SolenNewsSection from './components/SolenNewsSection';
import SolenInfoCards from './components/SolenInfoCards';
import SolenBoutiqueSection from './components/SolenBoutiqueSection';
import SolenBlogSection from './components/SolenBlogSection';

type HomePageProps = {
    productCatalogMap: SpecialPageProducts;
};

const HomePage: Page<HomePageProps> = memo(({productCatalogMap}) => {
    return (
        <>
            <Seo
                canonical={`${trim(process.env.NEXT_PUBLIC_SITE_URL, '/')}/`}
            />

            {/* Hero Banner Slider */}
            <SolenHeroBanner />

            {/* Brand Showcase Section */}
            <SolenBrandShowcase />

            {/* Video Section */}
            <SolenVideoSection />

            {/* News and Press Section */}
            <SolenNewsSection />

            {/* Info Cards Section */}
            <SolenInfoCards />

            {/* Boutique Section */}
            <SolenBoutiqueSection />

            {/* Blog Section */}
            <SolenBlogSection />
        </>
    );
});

if (isDev) {
    HomePage.displayName = 'HomePage';
}

HomePage.initPageProps = async props => {
    return props;
};

export default HomePage;
