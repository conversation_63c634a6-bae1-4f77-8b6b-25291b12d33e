import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {UiLink} from '@core/components/ui';

interface NewsItem {
    id: string;
    title: string;
    excerpt: string;
    href: string;
}

const newsItems: NewsItem[] = [
    {
        id: '1',
        title: 'European Candy Kettle Club "Professional Excellence" Ödülü Şölen\'in Oldu',
        excerpt: 'Şölen, European Candy Kettle Club tarafından verilen ve Avrupa şekerleme sektörünün en büyük ödülü olan "Professional Excellence"ı kazandı.',
        href: '/basin-bultenleri/european-candy-kettle-club-professional-excellence-odulu-solen-in-oldu'
    },
    {
        id: '2',
        title: 'Çocukların Yeni Favorisi; Sütlü Ballı Ozmo Cool',
        excerpt: 'Ozmo, çocukların lezzet dünyasını genişletmeye devam ediyor. İçinde bol sütlü ballı kreması ve yumuşak keki, dışında çikolatası ile Ozmo Cool, hem çocukların hem de ebeveynlerin yeni favorisi olacak.',
        href: '/basin-bultenleri/cocuklarin-yeni-favorisi-sutlu-balli-ozmo-cool'
    },
    {
        id: '3',
        title: 'Luppo Sufle Kendini Akışına Bırak',
        excerpt: 'Akışkan ve bol çikolatalı dolgusuyla dikkat çeken Luppo Sufle\'nin yeni reklam filmi izleyici ile buluşuyor.',
        href: '/basin-bultenleri/luppo-sufle-kendini-akisina-birak'
    },
    {
        id: '4',
        title: 'Amada Silver Felis Ödülünü Kazandı',
        excerpt: 'Amada, Curious Felis Ödülleri\'nde "Amada Mood - Don\'t Let the \'Buts\' Hold You Back" adlı iletişim kampanyasıyla Yaratıcı Strateji Bölümü Global Düşünenler kategorisinde Silver Felis ödülüne layık görüldü.',
        href: '/basin-bultenleri/amada-silver-felis-odulunu-kazandi'
    }
];

const SolenNewsSection: FC = memo(() => {
    return (
        <section className="solen-news-section bg-white py-16">
            <div className="container mx-auto px-4">
                {/* Section Header */}
                <div className="mb-12 text-center">
                    <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">
                        Basın Odası
                    </h2>
                </div>

                {/* News Grid */}
                <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-2">
                    {newsItems.map((item, index) => (
                        <article key={item.id} className={`news-item ${index === 0 ? 'md:col-span-2 lg:col-span-1' : ''}`}>
                            <UiLink
                                href={item.href}
                                className="group block h-full rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-all duration-300 hover:border-orange-300 hover:shadow-lg"
                            >
                                <h3 className="mb-4 text-xl font-semibold text-gray-900 group-hover:text-orange-600 lg:text-2xl">
                                    {item.title}
                                </h3>
                                <p className="text-gray-600 leading-relaxed">
                                    {item.excerpt}
                                </p>
                                <div className="mt-4 inline-flex items-center text-orange-600 group-hover:text-orange-700">
                                    <span className="text-sm font-medium">Devamını Oku</span>
                                    <svg 
                                        className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" 
                                        fill="none" 
                                        stroke="currentColor" 
                                        viewBox="0 0 24 24"
                                    >
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                </div>
                            </UiLink>
                        </article>
                    ))}
                </div>

                {/* View All Link */}
                <div className="mt-12 text-center">
                    <UiLink
                        href="/basin-bultenleri"
                        className="inline-block rounded-full bg-orange-500 px-8 py-3 text-white font-semibold transition-colors duration-300 hover:bg-orange-600"
                    >
                        Basın Bültenleri ve Basın Yansımalarına ulaşmak için tıklayınız
                    </UiLink>
                </div>
            </div>
        </section>
    );
});

if (isDev) {
    SolenNewsSection.displayName = 'SolenNewsSection';
}

export default SolenNewsSection;
