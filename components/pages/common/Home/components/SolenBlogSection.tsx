import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {UiImage, UiLink} from '@core/components/ui';

interface BlogPost {
    id: string;
    title: string;
    excerpt: string;
    href: string;
}

const blogPosts: BlogPost[] = [
    {
        id: '1',
        title: '<PERSON><PERSON>\'nda Gidilecek Yerler: 6 Tatil Önerisi',
        excerpt: 'Şeh<PERSON><PERSON> biraz uzaklaşmak, yeni yerler keşfetmek için bayram tatilleri oldukça uygun zamanlar.',
        href: 'https://www.solen.com.tr/blog/ramazan-bayraminda-gidilecek-yerler'
    },
    {
        id: '2',
        title: 'Küçük Çikolata Bombası: İzmir Bomba Tarifi',
        excerpt: 'İzmir\'in pek çok meşhur lezzeti olsa da İzmir bomba, son yıllarda herkesin kalbinde taht kurmuş durumda.',
        href: 'https://www.solen.com.tr/blog/tarif/izmir-bomba-tarifi'
    }
];

const SolenBlogSection: FC = memo(() => {
    return (
        <section className="solen-blog-section bg-gray-50 py-16">
            <div className="container mx-auto px-4">
                {/* Section Header */}
                <div className="mb-12 text-center">
                    <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">
                        Şölen Blog
                    </h2>
                    <p className="text-xl text-gray-600">
                        Hayatını Şölen'e Dönüştürmek Senin Elinde!
                    </p>
                </div>

                {/* Featured Blog Image */}
                <div className="mb-12">
                    <UiLink href="/blog" className="block">
                        <div className="relative overflow-hidden rounded-2xl shadow-lg">
                            <UiImage
                                src="/Assets/files/home-blog.jpg"
                                alt="Home Blog"
                                width={1200}
                                height={400}
                                className="w-full object-cover transition-transform duration-300 hover:scale-105"
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-20 transition-opacity duration-300 hover:bg-opacity-10"></div>
                        </div>
                    </UiLink>
                </div>

                {/* Blog Posts Grid */}
                <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                    {blogPosts.map((post) => (
                        <article key={post.id} className="blog-post">
                            <UiLink
                                href={post.href}
                                className="group block h-full rounded-lg bg-white p-6 shadow-md transition-all duration-300 hover:shadow-lg"
                            >
                                <h3 className="mb-4 text-xl font-semibold text-gray-900 group-hover:text-orange-600">
                                    {post.title}
                                </h3>
                                <p className="mb-4 text-gray-600 leading-relaxed">
                                    {post.excerpt}
                                </p>
                                <div className="inline-flex items-center text-orange-600 group-hover:text-orange-700">
                                    <span className="text-sm font-medium">Devamını Oku</span>
                                    <svg 
                                        className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" 
                                        fill="none" 
                                        stroke="currentColor" 
                                        viewBox="0 0 24 24"
                                    >
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                </div>
                            </UiLink>
                        </article>
                    ))}
                </div>

                {/* View All Blog Link */}
                <div className="mt-12 text-center">
                    <UiLink
                        href="/blog"
                        className="inline-block rounded-full bg-orange-500 px-8 py-3 text-white font-semibold transition-colors duration-300 hover:bg-orange-600"
                    >
                        Tüm Blog içeriklerine ulaşmak için tıklayınız
                    </UiLink>
                </div>
            </div>
        </section>
    );
});

if (isDev) {
    SolenBlogSection.displayName = 'SolenBlogSection';
}

export default SolenBlogSection;
