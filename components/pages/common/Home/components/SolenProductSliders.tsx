import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {UiImage, UiLink, UiSlider} from '@core/components/ui';
import {Autoplay} from '@core/components/ui/Slider';

interface ProductSliderData {
    id: string;
    brandName: string;
    mainImage: string;
    href: string;
    products: {
        id: string;
        image: string;
        alt: string;
    }[];
}

const productSliders: ProductSliderData[] = [
    {
        id: 'biscolata',
        brandName: 'Biscolata',
        mainImage:
            'https://solen.becdn.net/driveae345/solen/Assets/visuals/Bisco-v7.png',
        href: '/biscolata',
        products: [
            {
                id: 'cho010',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/cho010.png',
                alt: 'biscolata mood tekli'
            },
            {
                id: 'cho020',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/cho020.png',
                alt: 'biscolata mood tekli'
            },
            {
                id: 'cho030',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/cho030.png',
                alt: 'biscolata mood tekli'
            },
            {
                id: 'cho040',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/cho040.png',
                alt: 'biscolata mood tek'
            },
            {
                id: 'cho050',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/cho050.png',
                alt: 'biscolata mood tekli'
            },
            {
                id: 'cho070',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/cho070.png',
                alt: 'biscolata mood tekli'
            }
        ]
    },
    {
        id: 'boombastic',
        brandName: 'Boombastic',
        mainImage:
            'https://solen.becdn.net/driveae345/solen/Assets/visuals/Boombastik-Slider-v5.png',
        href: '/boombastic',
        products: [
            {
                id: 'bombastik-10',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/bombastik-10-v3.png',
                alt: 'şölen boombastic'
            },
            {
                id: 'bombastik-2',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Bombastik-2-v2.png',
                alt: 'şölen boombastic bar'
            },
            {
                id: 'bombastik-3',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Bombastik-3-v2.png',
                alt: 'boombastic dilim'
            }
        ]
    },
    {
        id: 'chocodans',
        brandName: 'Chocodans',
        mainImage:
            'https://solen.becdn.net/driveae345/solen/Assets/visuals/Chocodans-Slider-v5.png',
        href: '/chocodans',
        products: [
            {
                id: 'chocodans-1',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Chocodans-1.png',
                alt: 'şölen-chcodans tekli'
            },
            {
                id: 'chocodans-3',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Chocodans-3.png',
                alt: 'şölen chocodans tek'
            },
            {
                id: 'findik-11',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Findik-11.png',
                alt: 'fındık'
            },
            {
                id: 'findik-3',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Findik-3.png',
                alt: 'fındık'
            },
            {
                id: 'chocodans-2',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Chocodans-2.png',
                alt: 'chocodans dilim'
            },
            {
                id: 'chocodans-4',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Chocodans-4.png',
                alt: 'şölen chocodans tek'
            },
            {
                id: 'findik-2',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Findik-2.png',
                alt: 'fındık'
            }
        ]
    },
    {
        id: 'greta',
        brandName: 'Greta',
        mainImage:
            'https://solen.becdn.net/driveae345/solen/Assets/visuals/Greta-Slider-v3.png',
        href: '/greta',
        products: [
            {
                id: 'greta-3',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Greta-3.png',
                alt: 'şölen greta tek'
            },
            {
                id: 'greta-1',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Greta-1.png',
                alt: 'şölen greta dilim'
            },
            {
                id: 'kekik-1',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Kekik-1.png',
                alt: 'kekik'
            },
            {
                id: 'zeytin-6',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Zeytin-6.png',
                alt: 'zeytin'
            },
            {
                id: 'zeytin-1',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Zeytin-1.png',
                alt: 'zeytin'
            },
            {
                id: 'greta-2',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Greta-2.png',
                alt: 'şölen greta tekli'
            }
        ]
    },
    {
        id: 'luppo',
        brandName: 'Luppo',
        mainImage:
            'https://solen.becdn.net/driveae345/solen/Assets/visuals/Luppo-Slider-4.png',
        href: '/luppo',
        products: [
            {
                id: 'luppo-5',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Luppo-5.png',
                alt: 'luppo paket'
            },
            {
                id: 'luppo-1',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Luppo-1.png',
                alt: 'luppo sandviç kek'
            },
            {
                id: 'luppo-4',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Luppo-4.png',
                alt: 'luppo mini'
            },
            {
                id: 'luppo-8',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Luppo-8.png',
                alt: 'luppo tekli'
            },
            {
                id: 'luppo-7',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Luppo-7.png',
                alt: 'luppo dilim'
            },
            {
                id: 'luppo-3',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Luppo-3.png',
                alt: 'luppo paket'
            },
            {
                id: 'luppo-9',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Luppo-9.png',
                alt: 'luppo dilim tek'
            }
        ]
    },
    {
        id: 'nutymax',
        brandName: 'Nutymax',
        mainImage:
            'https://solen.becdn.net/driveae345/solen/Assets/visuals/Nutymax-Slider-v6.png',
        href: '/nutymax',
        products: [
            {
                id: 'nutymax-3',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Nutymax-3.png',
                alt: 'şölen nutymax'
            },
            {
                id: 'nutymax-2-2',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Nutymax-2-2.png',
                alt: 'şölen nutymax'
            },
            {
                id: 'antefFistigi-2',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/AntefFistigi-2.png',
                alt: 'şölen antep fıstığı'
            },
            {
                id: 'findik',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Findik.png',
                alt: 'şölen fındık'
            },
            {
                id: 'nutymax-4',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Nutymax-4.png',
                alt: 'şölen nutymax paket'
            },
            {
                id: 'nutymax-1',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Nutymax-1.png',
                alt: 'şölen nutymax'
            },
            {
                id: 'antefFistigi-1',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/AntefFistigi-1.png',
                alt: 'şölen antep fıstığı'
            }
        ]
    },
    {
        id: 'ozmo',
        brandName: 'Ozmo',
        mainImage:
            'https://solen.becdn.net/driveae345/solen/Assets/visuals/Ozmo-Slider-v3.png',
        href: '/ozmo',
        products: [
            {
                id: 'tavsan-v2',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Tavsan-v2.png',
                alt: 'şölen tavşan çikolata'
            },
            {
                id: 'partial10',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/partial10.png',
                alt: 'ozmo yumurta'
            },
            {
                id: 'ayi-v2',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Ayi-v2.png',
                alt: 'ozmo ayı çikolata'
            },
            {
                id: 'aslan-v2',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Aslan-v2.png',
                alt: 'ozmo aslan çikolata'
            },
            {
                id: 'partial13',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/partial13.png',
                alt: 'ozmo yumurta'
            },
            {
                id: 'partial14',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/partial14.png',
                alt: 'şölen yumurta çikolata'
            },
            {
                id: 'fil-v2',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Fil-v2.png',
                alt: 'ozmo fil çikolata'
            }
        ]
    },
    {
        id: 'papita',
        brandName: 'Papita',
        mainImage:
            'https://solen.becdn.net/driveae345/solen/Assets/visuals/Papita-Slider-1.png',
        href: '/papita',
        products: [
            {
                id: 'papita-v1',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Papita-v1.png',
                alt: 'şölen Papita'
            },
            {
                id: 'papita-v7',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Papita-v7.png',
                alt: 'şölen Papita'
            },
            {
                id: 'papita-2',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Papita-2.png',
                alt: 'şölen Papita'
            },
            {
                id: 'papita-3',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Papita-3.png',
                alt: 'şölen Papita'
            },
            {
                id: 'papita-v4',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Papita-v4.png',
                alt: 'şölen Papita'
            },
            {
                id: 'papita-6',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Papita-6.png',
                alt: 'şölen Papita'
            },
            {
                id: 'papita-5',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Papita-5.png',
                alt: 'şölen Papita'
            }
        ]
    },
    {
        id: 'ikramlik',
        brandName: 'İkramlik',
        mainImage:
            'https://solen.becdn.net/driveae345/solen/Assets/visuals/ikramlik-Slider-3.png',
        href: '/ikramlik',
        products: [
            {
                id: 'milango',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Milango.png',
                alt: 'şölen milango çikolata'
            },
            {
                id: 'butik',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Butik.png',
                alt: 'butik şeker'
            },
            {
                id: 'diamond',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Diamond.png',
                alt: 'şölen çikolata'
            },
            {
                id: 'mojee',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Mojee.png',
                alt: 'şölen mojee tekli'
            },
            {
                id: 'octavia',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Octavia.png',
                alt: 'şölen octavia tekli'
            },
            {
                id: 'vital-bitter',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Vital-Bitter.png',
                alt: 'şölen vital tekli'
            },
            {
                id: 'vital',
                image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Vital.png',
                alt: 'şölen vital tekli'
            }
        ]
    }
];

const SolenProductSliders: FC = memo(() => {
    return (
        <section className="solen-product-sliders bg-gray-50 py-16">
            <div className="container mx-auto px-4">
                {productSliders.map(slider => (
                    <div key={slider.id} className="mb-16 last:mb-0">
                        <div className="relative overflow-hidden rounded-2xl bg-white shadow-lg">
                            <UiLink href={slider.href} className="block">
                                <div className="relative h-64 md:h-80 lg:h-96">
                                    {/* Main Brand Image */}
                                    <div className="absolute left-8 top-1/2 z-10 -translate-y-1/2">
                                        <UiImage
                                            src={slider.mainImage}
                                            alt={`${slider.brandName} slider`}
                                            width={300}
                                            height={200}
                                            className="object-contain"
                                        />
                                    </div>

                                    {/* Floating Product Images */}
                                    <div className="absolute right-0 top-0 h-full w-2/3">
                                        <UiSlider
                                            className="product-floating-slider h-full"
                                            modules={[Autoplay]}
                                            autoplay={{
                                                delay: 3000,
                                                disableOnInteraction: false
                                            }}
                                            slidesPerView={3}
                                            spaceBetween={20}
                                            loop
                                            breakpoints={{
                                                768: {
                                                    slidesPerView: 4,
                                                    spaceBetween: 30
                                                },
                                                1024: {
                                                    slidesPerView: 5,
                                                    spaceBetween: 40
                                                }
                                            }}
                                        >
                                            {slider.products.map(product => (
                                                <UiSlider.Slide
                                                    key={product.id}
                                                >
                                                    <div className="flex h-full items-center justify-center p-4">
                                                        <UiImage
                                                            src={product.image}
                                                            alt={product.alt}
                                                            width={120}
                                                            height={120}
                                                            className="object-contain transition-transform duration-300 hover:scale-110"
                                                        />
                                                    </div>
                                                </UiSlider.Slide>
                                            ))}
                                        </UiSlider>
                                    </div>

                                    {/* Background Gradient */}
                                    <div className="absolute inset-0 bg-gradient-to-r from-white via-white/80 to-transparent"></div>
                                </div>
                            </UiLink>
                        </div>
                    </div>
                ))}
            </div>
        </section>
    );
});

if (isDev) {
    SolenProductSliders.displayName = 'SolenProductSliders';
}

export default SolenProductSliders;
