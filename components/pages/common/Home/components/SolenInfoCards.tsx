import {FC, memo, useState, useEffect} from 'react';
import {isDev} from '@core/helpers';
import {UiImage} from '@core/components/ui';

interface InfoCard {
    id: string;
    title: string;
    description: string;
    image: string;
    alt: string;
}

const infoCards: InfoCard[] = [
    {
        id: '1',
        title: 'Bunları biliyor musunuz?',
        description: 'Türkiye\'nin Çikolata Fabrikası olarak adlandırılan Gaziantep fabrikası, endüstri 4.0 donanımı ve ileri teknolojisiyle dünyanın sayılı tesisleri arasında yer alıyor. 17 futbol sahası büyüklüğünde alana kurulu tesis, günlük 900 ton üretim kapasitesine sahip.',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/HaritaveCikolata-2-1-3.png',
        alt: 'şölen fabrika'
    },
    {
        id: '2',
        title: 'Bunları biliyor musunuz?',
        description: 'Biscolata markasının ürün gamı Amazon.com\'da satışa sunuluyor. En çok satılan 15 üründen biri olan Biscolata, Amazon Fresh programında satışa sunularak 1 saatlik sürede teslimatı gerçekleştiriliyor.',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/biscolata-pia-sec-v2.png',
        alt: 'biscolata pia'
    },
    {
        id: '3',
        title: 'Bunları biliyor musunuz?',
        description: 'Fabrika\'nın üretim hattının toplam uzunluğu, dünyanın en uzun binası Burc Halife\'in yüksekliğinin 15 katı.',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/burc-halife.png',
        alt: 'şölen burç halife'
    },
    {
        id: '4',
        title: 'Bunları biliyor musunuz?',
        description: 'Şölen olarak ihracat odaklı çalışıyor, 200 ürün çeşidini Belçika\'dan Maldivler\'e, Kolombiya\'dan Japonya\'ya kadar 120\'den fazla ülkeye ulaştırıyoruz.',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/solen-ihracat.png',
        alt: 'şölen ihracat'
    }
];

const SolenInfoCards: FC = memo(() => {
    const [currentCard, setCurrentCard] = useState(0);

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentCard((prev) => (prev + 1) % infoCards.length);
        }, 5000); // Change card every 5 seconds

        return () => clearInterval(interval);
    }, []);

    const currentInfo = infoCards[currentCard];

    return (
        <section className="solen-info-cards bg-gradient-to-br from-blue-50 to-indigo-100 py-16">
            <div className="container mx-auto px-4">
                <div className="mx-auto max-w-4xl">
                    <div className="overflow-hidden rounded-2xl bg-white shadow-xl">
                        <div className="flex flex-col lg:flex-row">
                            {/* Text Content */}
                            <div className="flex-1 p-8 lg:p-12">
                                <h2 className="mb-6 text-2xl font-bold text-orange-600 md:text-3xl">
                                    {currentInfo.title}
                                </h2>
                                <p className="text-lg leading-relaxed text-gray-700">
                                    {currentInfo.description}
                                </p>

                                {/* Card Indicators */}
                                <div className="mt-8 flex space-x-2">
                                    {infoCards.map((_, index) => (
                                        <button
                                            key={index}
                                            onClick={() => setCurrentCard(index)}
                                            className={`h-3 w-3 rounded-full transition-colors duration-300 ${
                                                index === currentCard
                                                    ? 'bg-orange-500'
                                                    : 'bg-gray-300 hover:bg-gray-400'
                                            }`}
                                            aria-label={`Go to card ${index + 1}`}
                                        />
                                    ))}
                                </div>
                            </div>

                            {/* Image Content */}
                            <div className="flex-1 p-8 lg:p-12">
                                <div className="relative h-64 lg:h-80">
                                    <UiImage
                                        src={currentInfo.image}
                                        alt={currentInfo.alt}
                                        fill
                                        className="object-contain"
                                        sizes="(max-width: 1024px) 100vw, 50vw"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
});

if (isDev) {
    SolenInfoCards.displayName = 'SolenInfoCards';
}

export default SolenInfoCards;
