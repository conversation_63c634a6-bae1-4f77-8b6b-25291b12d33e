import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {UiImage, UiLink} from '@core/components/ui';

const SolenVideoSection: FC = memo(() => {
    return (
        <section className="solen-video-section relative bg-gradient-to-b from-orange-50 to-orange-100 py-16">
            {/* Smoke Effect */}
            <div className="absolute left-0 top-0 z-10">
                <UiImage
                    src="https://solen.becdn.net/driveae345/solen/Assets/visuals/smoke.png"
                    alt="duman"
                    width={200}
                    height={200}
                    className="opacity-60"
                />
            </div>

            <div className=" relative z-20 mx-auto px-4">
                <div className="flex flex-col items-center lg:flex-row lg:items-center lg:justify-between">
                    {/* Left Side - Biscoman Character */}
                    <div className="mb-8 lg:mb-0 lg:w-1/2">
                        <div className="relative">
                            <UiImage
                                src="https://solen.becdn.net/driveae345/solen/Assets/visuals/biscoman.jpg?v=3"
                                alt="biscolata biscoman"
                                width={400}
                                height={500}
                                className="mx-auto rounded-lg shadow-lg"
                            />
                        </div>
                    </div>

                    {/* Right Side - Video Content */}
                    <div className="text-center lg:w-1/2 lg:text-left">
                        <h2 className="mb-6 text-3xl font-bold text-gray-900 md:text-4xl">
                            Reklam Filmleri
                        </h2>

                        <div className="mb-8">
                            <UiImage
                                src="https://solen.becdn.net/driveae345/solen/Assets/visuals/smoke.png"
                                alt="duman"
                                width={150}
                                height={150}
                                className="mx-auto opacity-40 lg:mx-0"
                            />
                        </div>

                        <p className="mb-8 text-lg text-gray-700">
                            Reklam Filmlerimizi İzlemek İçin
                        </p>

                        <UiLink
                            href="/reklam-filmleri"
                            className="solen-pulse inline-block rounded-full bg-orange-500 px-8 py-4 text-lg font-semibold text-white transition-all duration-300 hover:scale-105 hover:bg-orange-600"
                        >
                            TIKLAYINIZ
                        </UiLink>

                        {/* Video Link */}
                        <div className="mt-8">
                            <a
                                href="https://www.youtube.com/watch?v=TOmGACM8tAM&list=PLBtRBZ88WhEA3nQ-fpGOOZBEXOvFz4jTb"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-block text-orange-600 hover:text-orange-700"
                            >
                                Filmi İzlemek İçin Tıklayın
                            </a>
                        </div>
                    </div>
                </div>

                {/* Bottom Action Cards */}
                <div className="mt-16 grid grid-cols-1 gap-6 md:grid-cols-3">
                    {/* Şölen Boutique */}
                    <a
                        href="https://solenboutique.com/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="solen-hover-lift group block rounded-xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-2xl"
                    >
                        <div className="flex items-center space-x-6">
                            <div className="solen-float-animation">
                                <UiImage
                                    src="https://solen.becdn.net/driveae345/solen/Assets/visuals/mobil-solenBoutiqe-Logo.svg"
                                    alt="şölen boutique logo"
                                    width={70}
                                    height={70}
                                />
                            </div>
                            <div>
                                <h3 className="text-xl font-semibold text-gray-900 transition-colors duration-300 group-hover:text-orange-600">
                                    Şölen Boutique
                                </h3>
                            </div>
                        </div>
                    </a>

                    {/* Markalar */}
                    <UiLink
                        href="/markalarimiz"
                        className="solen-hover-lift group block rounded-xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-2xl"
                    >
                        <div className="flex items-center space-x-6">
                            <div
                                className="solen-float-animation"
                                style={{animationDelay: '0.5s'}}
                            >
                                <UiImage
                                    src="https://solen.becdn.net/driveae345/solen/Assets/visuals/badge01.png"
                                    alt="şölen madalya ikon"
                                    width={70}
                                    height={70}
                                />
                            </div>
                            <div>
                                <h3 className="text-xl font-semibold text-gray-900 transition-colors duration-300 group-hover:text-orange-600">
                                    Markalar
                                </h3>
                            </div>
                        </div>
                    </UiLink>

                    {/* Bize Ulaşın */}
                    <UiLink
                        href="/iletisim"
                        className="solen-hover-lift group block rounded-xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-2xl"
                    >
                        <div className="flex items-center space-x-6">
                            <div
                                className="solen-float-animation"
                                style={{animationDelay: '1s'}}
                            >
                                <UiImage
                                    src="https://solen.becdn.net/driveae345/solen/Assets/visuals/badge02.png"
                                    alt="şölen telefon ikon"
                                    width={70}
                                    height={70}
                                />
                            </div>
                            <div>
                                <h3 className="text-xl font-semibold text-gray-900 transition-colors duration-300 group-hover:text-orange-600">
                                    Bize Ulaşın
                                </h3>
                            </div>
                        </div>
                    </UiLink>
                </div>
            </div>
        </section>
    );
});

if (isDev) {
    SolenVideoSection.displayName = 'SolenVideoSection';
}

export default SolenVideoSection;
