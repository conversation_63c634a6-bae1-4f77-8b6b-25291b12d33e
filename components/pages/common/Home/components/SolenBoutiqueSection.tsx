import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {UiImage} from '@core/components/ui';

const SolenBoutiqueSection: FC = memo(() => {
    return (
        <section className="solen-boutique-section bg-white py-16">
            <div className="container mx-auto px-4">
                <div className="grid grid-cols-1 gap-12 lg:grid-cols-1 lg:gap-16">
                    {/* Şölen Boutique */}
                    <div className="boutique-card">
                        <div className="rounded-2xl bg-gradient-to-br from-purple-50 to-pink-50 p-8 shadow-lg">
                            <div className="text-center">
                                <h2 className="mb-4 text-2xl font-bold text-gray-900 md:text-3xl">
                                    Her Anınızda Şölen Boutique
                                </h2>
                                <p className="mb-8 leading-relaxed text-gray-600">
                                    Ş<PERSON><PERSON>'in, klasikleşen ikramlık ve hediyelik
                                    çikolatalarının yanı sıra özel olarak
                                    hazırlanan gurme tatları Şölen Boutique'te!
                                </p>
                                <a
                                    href="https://solenboutique.com/"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="inline-block rounded-full bg-purple-600 px-8 py-3 font-semibold text-white transition-colors duration-300 hover:bg-purple-700"
                                >
                                    Şölen Boutique
                                </a>
                            </div>
                            <div className="mt-8 flex justify-center">
                                <UiImage
                                    src="https://solen.becdn.net/driveae345/solen/Assets/visuals/solen-boutique.png"
                                    alt="şölen boutique paket"
                                    width={300}
                                    height={200}
                                    className="object-contain"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Katalog Section */}
                    <div className="catalog-card">
                        <div className="rounded-2xl bg-gradient-to-br from-orange-50 to-red-50 p-8 shadow-lg">
                            <div className="text-center">
                                <div className="mb-6 flex justify-center">
                                    <UiImage
                                        src="/Assets/files/katalog/katalog-tr-v3.png"
                                        alt="şölen katalog"
                                        width={200}
                                        height={250}
                                        className="object-contain"
                                    />
                                </div>
                                <h2 className="mb-4 text-2xl font-bold text-gray-900 md:text-3xl">
                                    Şölen Lezzet Kataloğu
                                </h2>
                                <p className="mb-8 leading-relaxed text-gray-600">
                                    Şölen lezzet ailesinin 2025 yılı kataloğu
                                    ile çikolata ve çikolatalı ürünleri sevenler
                                    için görsel şölen sunulmaktadır.
                                </p>
                                <div className="flex flex-col space-y-4 sm:flex-row sm:justify-center sm:space-x-4 sm:space-y-0">
                                    <a
                                        href="/Assets/visuals/Solen-katalog-2025.pdf"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="inline-block rounded-full bg-orange-500 px-6 py-3 font-semibold text-white transition-colors duration-300 hover:bg-orange-600"
                                    >
                                        Ürün Kataloğu
                                    </a>
                                    <a
                                        href="/Assets/files/katalog/2025-bayram-katalogu.pdf"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="inline-block rounded-full border-2 border-orange-500 px-6 py-3 font-semibold text-orange-500 transition-colors duration-300 hover:bg-orange-500 hover:text-white"
                                    >
                                        Bayram Kataloğu
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
});

if (isDev) {
    SolenBoutiqueSection.displayName = 'SolenBoutiqueSection';
}

export default SolenBoutiqueSection;
