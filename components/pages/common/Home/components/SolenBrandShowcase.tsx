import {FC, memo, useState} from 'react';
import {isDev} from '@core/helpers';
import {UiImage, UiButton} from '@core/components/ui';

interface BrandItem {
    id: string;
    name: string;
    image: string;
    href: string;
    alt: string;
    products: ProductItem[];
}

interface ProductItem {
    id: string;
    image: string;
    alt: string;
}

const brands: BrandItem[] = [
    {
        id: 'biscolata',
        name: 'Biscolata',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/biscolata-v5.png',
        href: '/biscolata',
        alt: 'biscolata paket',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/200x200/8B4513/ffffff?text=Biscolata+1',
                alt: 'Biscolata Ürün 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/200x200/8B4513/ffffff?text=Biscolata+2',
                alt: 'Biscolata Ürün 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/200x200/8B4513/ffffff?text=Biscolata+3',
                alt: 'Biscolata Ürün 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/200x200/8B4513/ffffff?text=Biscolata+4',
                alt: 'Biscolata Ürün 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/200x200/8B4513/ffffff?text=Biscolata+5',
                alt: 'Biscolata Ürün 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/200x200/8B4513/ffffff?text=Biscolata+6',
                alt: 'Biscolata Ürün 6'
            }
        ]
    },
    {
        id: 'ozmo',
        name: 'Ozmo',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/ozmo-v6.png',
        href: '/ozmo',
        alt: 'şölen ozmo',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/200x200/FF6B35/ffffff?text=Ozmo+1',
                alt: 'Ozmo Ürün 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/200x200/FF6B35/ffffff?text=Ozmo+2',
                alt: 'Ozmo Ürün 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/200x200/FF6B35/ffffff?text=Ozmo+3',
                alt: 'Ozmo Ürün 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/200x200/FF6B35/ffffff?text=Ozmo+4',
                alt: 'Ozmo Ürün 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/200x200/FF6B35/ffffff?text=Ozmo+5',
                alt: 'Ozmo Ürün 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/200x200/FF6B35/ffffff?text=Ozmo+6',
                alt: 'Ozmo Ürün 6'
            }
        ]
    },
    {
        id: 'chocodans',
        name: 'Chocodans',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Chocodans-v4.png',
        href: '/chocodans',
        alt: 'şölen chocodans',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/200x200/D2691E/ffffff?text=Chocodans+1',
                alt: 'Chocodans Ürün 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/200x200/D2691E/ffffff?text=Chocodans+2',
                alt: 'Chocodans Ürün 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/200x200/D2691E/ffffff?text=Chocodans+3',
                alt: 'Chocodans Ürün 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/200x200/D2691E/ffffff?text=Chocodans+4',
                alt: 'Chocodans Ürün 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/200x200/D2691E/ffffff?text=Chocodans+5',
                alt: 'Chocodans Ürün 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/200x200/D2691E/ffffff?text=Chocodans+6',
                alt: 'Chocodans Ürün 6'
            }
        ]
    },
    {
        id: 'boombastic',
        name: 'Boombastic',
        image: 'https://via.placeholder.com/300x300/ef4444/ffffff?text=Boombastic',
        href: '/boombastic',
        alt: 'şölen boombastic çeşitleri',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/200x200/ef4444/ffffff?text=Boombastic+1',
                alt: 'Boombastic Ürün 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/200x200/ef4444/ffffff?text=Boombastic+2',
                alt: 'Boombastic Ürün 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/200x200/ef4444/ffffff?text=Boombastic+3',
                alt: 'Boombastic Ürün 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/200x200/ef4444/ffffff?text=Boombastic+4',
                alt: 'Boombastic Ürün 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/200x200/ef4444/ffffff?text=Boombastic+5',
                alt: 'Boombastic Ürün 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/200x200/ef4444/ffffff?text=Boombastic+6',
                alt: 'Boombastic Ürün 6'
            }
        ]
    },
    {
        id: 'greta',
        name: 'Greta',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Greta-v2.png',
        href: '/greta',
        alt: 'şölen greta çeşitleri',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/200x200/9932CC/ffffff?text=Greta+1',
                alt: 'Greta Ürün 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/200x200/9932CC/ffffff?text=Greta+2',
                alt: 'Greta Ürün 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/200x200/9932CC/ffffff?text=Greta+3',
                alt: 'Greta Ürün 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/200x200/9932CC/ffffff?text=Greta+4',
                alt: 'Greta Ürün 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/200x200/9932CC/ffffff?text=Greta+5',
                alt: 'Greta Ürün 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/200x200/9932CC/ffffff?text=Greta+6',
                alt: 'Greta Ürün 6'
            }
        ]
    },
    {
        id: 'luppo',
        name: 'Luppo',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/luppo-v4.png',
        href: '/luppo',
        alt: 'şölen luppo',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/200x200/32CD32/ffffff?text=Luppo+1',
                alt: 'Luppo Ürün 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/200x200/32CD32/ffffff?text=Luppo+2',
                alt: 'Luppo Ürün 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/200x200/32CD32/ffffff?text=Luppo+3',
                alt: 'Luppo Ürün 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/200x200/32CD32/ffffff?text=Luppo+4',
                alt: 'Luppo Ürün 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/200x200/32CD32/ffffff?text=Luppo+5',
                alt: 'Luppo Ürün 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/200x200/32CD32/ffffff?text=Luppo+6',
                alt: 'Luppo Ürün 6'
            }
        ]
    },
    {
        id: 'nutymax',
        name: 'Nutymax',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Nutymax-v2.png',
        href: '/nutymax',
        alt: 'şölen nutymax paketler',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/200x200/8B4513/ffffff?text=Nutymax+1',
                alt: 'Nutymax Ürün 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/200x200/8B4513/ffffff?text=Nutymax+2',
                alt: 'Nutymax Ürün 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/200x200/8B4513/ffffff?text=Nutymax+3',
                alt: 'Nutymax Ürün 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/200x200/8B4513/ffffff?text=Nutymax+4',
                alt: 'Nutymax Ürün 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/200x200/8B4513/ffffff?text=Nutymax+5',
                alt: 'Nutymax Ürün 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/200x200/8B4513/ffffff?text=Nutymax+6',
                alt: 'Nutymax Ürün 6'
            }
        ]
    },
    {
        id: 'papita',
        name: 'Papita',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Papita-v2.png',
        href: '/papita',
        alt: 'şölen papita paketler',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/200x200/FFD700/000000?text=Papita+1',
                alt: 'Papita Ürün 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/200x200/FFD700/000000?text=Papita+2',
                alt: 'Papita Ürün 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/200x200/FFD700/000000?text=Papita+3',
                alt: 'Papita Ürün 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/200x200/FFD700/000000?text=Papita+4',
                alt: 'Papita Ürün 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/200x200/FFD700/000000?text=Papita+5',
                alt: 'Papita Ürün 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/200x200/FFD700/000000?text=Papita+6',
                alt: 'Papita Ürün 6'
            }
        ]
    },
    {
        id: 'ikramlik',
        name: 'İkramlik',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Ikramlik-v4.png',
        href: '/ikramlik',
        alt: 'şölen İkramliklar',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/200x200/FF1493/ffffff?text=İkramlik+1',
                alt: 'İkramlik Ürün 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/200x200/FF1493/ffffff?text=İkramlik+2',
                alt: 'İkramlik Ürün 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/200x200/FF1493/ffffff?text=İkramlik+3',
                alt: 'İkramlik Ürün 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/200x200/FF1493/ffffff?text=İkramlik+4',
                alt: 'İkramlik Ürün 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/200x200/FF1493/ffffff?text=İkramlik+5',
                alt: 'İkramlik Ürün 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/200x200/FF1493/ffffff?text=İkramlik+6',
                alt: 'İkramlik Ürün 6'
            }
        ]
    }
];

const SolenBrandShowcase: FC = memo(() => {
    const [selectedBrand, setSelectedBrand] = useState<string>('biscolata');

    const currentBrand =
        brands.find(brand => brand.id === selectedBrand) || brands[0];

    return (
        <section className="solen-brand-showcase solen-gradient-bg py-20">
            <div className="container mx-auto px-4">
                {/* Section Title */}
                <div className="mb-16 text-center">
                    <h2 className="solen-text-shadow mb-6 text-2xl font-light tracking-wider text-gray-600 md:text-4xl">
                        KEŞFETMEK İÇİN TIKLA
                    </h2>
                    <h1 className="solen-text-shadow text-5xl font-bold text-gray-900 md:text-7xl lg:text-8xl">
                        İKRAMLIKLAR
                    </h1>
                </div>

                {/* Brand Navigation */}
                <div className="mb-12 flex justify-center">
                    <div className="inline-flex rounded-full bg-gradient-to-r from-pink-500 to-red-500 p-1 shadow-lg">
                        <div className="flex flex-wrap gap-1 rounded-full bg-white/90 p-2">
                            {brands.map(brand => (
                                <UiButton
                                    key={brand.id}
                                    onClick={() => setSelectedBrand(brand.id)}
                                    className={`
                                        rounded-full px-4 py-2 text-sm font-medium transition-all duration-300
                                        ${
                                            selectedBrand === brand.id
                                                ? 'bg-gradient-to-r from-pink-500 to-red-500 text-white shadow-md'
                                                : 'bg-transparent text-gray-700 hover:bg-gray-100'
                                        }
                                    `}
                                >
                                    {brand.name}
                                </UiButton>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Products Slider */}
                <div className="relative">
                    {/* Selected Brand Display */}
                    <div className="mb-8 text-center">
                        <div className="inline-flex items-center gap-4 rounded-2xl bg-white/80 px-8 py-4 shadow-lg backdrop-blur-sm">
                            <div className="relative h-16 w-16">
                                <UiImage
                                    src={currentBrand.image}
                                    alt={currentBrand.alt}
                                    fill
                                    className="object-contain"
                                />
                            </div>
                            <h3 className="text-2xl font-bold text-gray-800">
                                {currentBrand.name}
                            </h3>
                        </div>
                    </div>

                    {/* Products Grid */}
                    <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-6">
                        {currentBrand.products.map((product, index) => (
                            <div
                                key={product.id}
                                className="group relative overflow-hidden rounded-xl bg-white shadow-lg transition-all duration-500 hover:-translate-y-2 hover:shadow-2xl"
                                style={{
                                    animationDelay: `${index * 0.1}s`
                                }}
                            >
                                <div className="aspect-square relative p-4">
                                    <UiImage
                                        src={product.image}
                                        alt={product.alt}
                                        fill
                                        className="object-contain transition-transform duration-500 group-hover:scale-110"
                                        sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 16vw"
                                    />
                                    {/* Hover overlay */}
                                    <div className="absolute inset-0 rounded-xl bg-gradient-to-t from-pink-500/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
});

if (isDev) {
    SolenBrandShowcase.displayName = 'SolenBrandShowcase';
}

export default SolenBrandShowcase;
