import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {UiImage, UiLink} from '@core/components/ui';

interface BrandItem {
    id: string;
    name: string;
    image: string;
    href: string;
    alt: string;
}

const brands: BrandItem[] = [
    {
        id: 'biscolata',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/biscolata-v5.png',
        href: '/biscolata',
        alt: 'biscolata paket'
    },
    {
        id: 'ozmo',
        name: 'Ozmo',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/ozmo-v6.png',
        href: '/ozmo',
        alt: 'şölen ozmo'
    },
    {
        id: 'chocodans',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Chocodans-v4.png',
        href: '/chocodans',
        alt: 'ş<PERSON>len chocodans'
    },
    {
        id: 'boombastic',
        name: '<PERSON><PERSON><PERSON>',
        image: 'https://via.placeholder.com/300x300/ef4444/ffffff?text=Boombastic',
        href: '/boombastic',
        alt: 'şölen boombastic çeşitleri'
    },
    {
        id: 'greta',
        name: 'Greta',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Greta-v2.png',
        href: '/greta',
        alt: 'şölen greta çeşitleri'
    },
    {
        id: 'luppo',
        name: 'Luppo',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/luppo-v4.png',
        href: '/luppo',
        alt: 'şölen luppo'
    },
    {
        id: 'nutymax',
        name: 'Nutymax',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Nutymax-v2.png',
        href: '/nutymax',
        alt: 'şölen nutymax paketler'
    },
    {
        id: 'papita',
        name: 'Papita',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Papita-v2.png',
        href: '/papita',
        alt: 'şölen papita paketler'
    },
    {
        id: 'ikramlik',
        name: 'İkramlik',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Ikramlik-v4.png',
        href: '/ikramlik',
        alt: 'şölen İkramliklar'
    }
];

const SolenBrandShowcase: FC = memo(() => {
    return (
        <section className="solen-brand-showcase solen-gradient-bg py-20">
            <div className="container mx-auto px-4">
                {/* Section Title */}
                <div className="mb-16 text-center">
                    <h2 className="solen-text-shadow mb-6 text-2xl font-light tracking-wider text-gray-600 md:text-4xl">
                        HAYATIN HER ANINDA
                    </h2>
                    <h1 className="solen-text-shadow text-5xl font-bold text-gray-900 md:text-7xl lg:text-8xl">
                        ŞÖLEN
                    </h1>
                </div>

                {/* Brand Grid */}
                <div className="grid grid-cols-3 gap-6 md:gap-10 lg:gap-16">
                    {brands.map((brand, index) => (
                        <div key={brand.id} className="brand-item group">
                            <UiLink
                                href={brand.href}
                                className="solen-hover-lift block"
                            >
                                <div className="aspect-square relative overflow-hidden rounded-2xl bg-white p-6 shadow-lg transition-all duration-500 hover:shadow-2xl">
                                    <UiImage
                                        src={brand.image}
                                        alt={brand.alt}
                                        fill
                                        className="object-contain p-3 transition-transform duration-500 group-hover:scale-110"
                                        sizes="(max-width: 768px) 33vw, (max-width: 1200px) 25vw, 20vw"
                                        style={{
                                            animationDelay: `${index * 0.1}s`
                                        }}
                                    />
                                    {/* Hover overlay */}
                                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-orange-500/10 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                                </div>
                            </UiLink>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
});

if (isDev) {
    SolenBrandShowcase.displayName = 'SolenBrandShowcase';
}

export default SolenBrandShowcase;
