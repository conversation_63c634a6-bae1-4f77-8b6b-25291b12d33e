import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {UiImage, UiLink, UiSlider} from '@core/components/ui';
import {Autoplay, EffectFade, Navigation} from '@core/components/ui/Slider';

interface BannerSlide {
    id: string;
    image: string;
    link?: string;
    alt: string;
}

interface SolenHeroBannerProps {
    slides?: BannerSlide[];
}

const defaultSlides: BannerSlide[] = [
    {
        id: '1',
        image: '/Assets/files/surdurulebilirlik-rapuru-dekstop-tr-v3.png',
        link: '/surdurulebilirlik',
        alt: 'Sürdürülebilirlik Raporu'
    },
    {
        id: '2',
        image: '/Assets/files/ozmo-cok-sekil-eglence-desktop.jpeg',
        link: 'https://www.youtube.com/watch?v=70DijfaV-eQ',
        alt: 'Ozmo Çok Şekil Eğlence'
    },
    {
        id: '3',
        image: '/Assets/files/biscolata-banner-desktop.jpg',
        link: 'https://www.youtube.com/watch?v=AQxzzQi9gKg',
        alt: 'Biscolata'
    },
    {
        id: '4',
        image: '/Assets/files/nutymax-banner-desktop.jpg',
        link: 'https://www.youtube.com/watch?v=dNtMNvlSfrw',
        alt: 'Nutymax'
    },
    {
        id: '5',
        image: '/Assets/files/ozmo-banner-desktop.jpg',
        link: 'https://www.youtube.com/watch?v=AwI9RGae77c',
        alt: 'Ozmo'
    },
    {
        id: '6',
        image: '/Assets/files/boombastik-deskop-banner-v2.jpg',
        link: 'https://www.youtube.com/watch?v=ee9jETjDpB8',
        alt: 'Boombastic'
    }
];

const SolenHeroBanner: FC<SolenHeroBannerProps> = memo(({slides = defaultSlides}) => {
    return (
        <div className="solen-hero-banner w-full">
            <div className="aspect-h-4 aspect-w-10 w-full md:aspect-h-3 lg:aspect-h-2">
                <div>
                    <UiSlider
                        className="h-full"
                        modules={[Autoplay, EffectFade, Navigation]}
                        autoplay={{
                            delay: 5000,
                            disableOnInteraction: false
                        }}
                        effect="fade"
                        fadeEffect={{
                            crossFade: true
                        }}
                        loop
                        navigation
                    >
                        {slides.map((slide, index) => (
                            <UiSlider.Slide key={slide.id}>
                                {slide.link ? (
                                    <UiLink
                                        className="relative block h-full w-full"
                                        href={slide.link}
                                        target={slide.link.startsWith('http') ? '_blank' : '_self'}
                                    >
                                        <UiImage
                                            src={slide.image}
                                            alt={slide.alt}
                                            fit="cover"
                                            position="center"
                                            priority={index === 0}
                                            fill
                                        />
                                    </UiLink>
                                ) : (
                                    <div className="relative h-full w-full">
                                        <UiImage
                                            src={slide.image}
                                            alt={slide.alt}
                                            fit="cover"
                                            position="center"
                                            priority={index === 0}
                                            fill
                                        />
                                    </div>
                                )}
                            </UiSlider.Slide>
                        ))}
                    </UiSlider>
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    SolenHeroBanner.displayName = 'SolenHeroBanner';
}

export default SolenHeroBanner;
