import {FC, memo, useState} from 'react';
import {isDev} from '@core/helpers';
import {UiImage, UiButton} from '@core/components/ui';

interface BrandCategory {
    id: string;
    name: string;
    image: string;
    backgroundImage: string;
    products: ProductItem[];
}

interface ProductItem {
    id: string;
    image: string;
    alt: string;
}

interface SolenHeroBannerProps {
    categories?: BrandCategory[];
}

const defaultCategories: BrandCategory[] = [
    {
        id: 'biscolata',
        name: 'Biscolata',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/biscolata-v5.png',
        backgroundImage:
            'https://via.placeholder.com/1200x600/8B4513/ffffff?text=Biscolata+Background',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/150x150/8B4513/ffffff?text=B1',
                alt: 'Biscolata 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/150x150/8B4513/ffffff?text=B2',
                alt: 'Biscolata 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/150x150/8B4513/ffffff?text=B3',
                alt: 'Biscolata 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/150x150/8B4513/ffffff?text=B4',
                alt: 'Biscolata 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/150x150/8B4513/ffffff?text=B5',
                alt: 'Biscolata 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/150x150/8B4513/ffffff?text=B6',
                alt: 'Biscolata 6'
            }
        ]
    },
    {
        id: 'ozmo',
        name: 'Ozmo',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/ozmo-v6.png',
        backgroundImage:
            'https://via.placeholder.com/1200x600/FF6B35/ffffff?text=Ozmo+Background',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/150x150/FF6B35/ffffff?text=O1',
                alt: 'Ozmo 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/150x150/FF6B35/ffffff?text=O2',
                alt: 'Ozmo 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/150x150/FF6B35/ffffff?text=O3',
                alt: 'Ozmo 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/150x150/FF6B35/ffffff?text=O4',
                alt: 'Ozmo 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/150x150/FF6B35/ffffff?text=O5',
                alt: 'Ozmo 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/150x150/FF6B35/ffffff?text=O6',
                alt: 'Ozmo 6'
            }
        ]
    },
    {
        id: 'chocodans',
        name: 'Chocodans',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Chocodans-v4.png',
        backgroundImage:
            'https://via.placeholder.com/1200x600/D2691E/ffffff?text=Chocodans+Background',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/150x150/D2691E/ffffff?text=C1',
                alt: 'Chocodans 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/150x150/D2691E/ffffff?text=C2',
                alt: 'Chocodans 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/150x150/D2691E/ffffff?text=C3',
                alt: 'Chocodans 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/150x150/D2691E/ffffff?text=C4',
                alt: 'Chocodans 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/150x150/D2691E/ffffff?text=C5',
                alt: 'Chocodans 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/150x150/D2691E/ffffff?text=C6',
                alt: 'Chocodans 6'
            }
        ]
    },
    {
        id: 'greta',
        name: 'Greta',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Greta-v2.png',
        backgroundImage:
            'https://via.placeholder.com/1200x600/9932CC/ffffff?text=Greta+Background',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/150x150/9932CC/ffffff?text=G1',
                alt: 'Greta 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/150x150/9932CC/ffffff?text=G2',
                alt: 'Greta 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/150x150/9932CC/ffffff?text=G3',
                alt: 'Greta 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/150x150/9932CC/ffffff?text=G4',
                alt: 'Greta 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/150x150/9932CC/ffffff?text=G5',
                alt: 'Greta 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/150x150/9932CC/ffffff?text=G6',
                alt: 'Greta 6'
            }
        ]
    },
    {
        id: 'luppo',
        name: 'Luppo',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/luppo-v4.png',
        backgroundImage:
            'https://via.placeholder.com/1200x600/32CD32/ffffff?text=Luppo+Background',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/150x150/32CD32/ffffff?text=L1',
                alt: 'Luppo 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/150x150/32CD32/ffffff?text=L2',
                alt: 'Luppo 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/150x150/32CD32/ffffff?text=L3',
                alt: 'Luppo 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/150x150/32CD32/ffffff?text=L4',
                alt: 'Luppo 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/150x150/32CD32/ffffff?text=L5',
                alt: 'Luppo 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/150x150/32CD32/ffffff?text=L6',
                alt: 'Luppo 6'
            }
        ]
    },
    {
        id: 'ikramlik',
        name: 'İkramliklar',
        image: 'https://solen.becdn.net/driveae345/solen/Assets/visuals/Ikramlik-v4.png',
        backgroundImage:
            'https://via.placeholder.com/1200x600/FF1493/ffffff?text=İkramliklar+Background',
        products: [
            {
                id: '1',
                image: 'https://via.placeholder.com/150x150/FF1493/ffffff?text=İ1',
                alt: 'İkramlik 1'
            },
            {
                id: '2',
                image: 'https://via.placeholder.com/150x150/FF1493/ffffff?text=İ2',
                alt: 'İkramlik 2'
            },
            {
                id: '3',
                image: 'https://via.placeholder.com/150x150/FF1493/ffffff?text=İ3',
                alt: 'İkramlik 3'
            },
            {
                id: '4',
                image: 'https://via.placeholder.com/150x150/FF1493/ffffff?text=İ4',
                alt: 'İkramlik 4'
            },
            {
                id: '5',
                image: 'https://via.placeholder.com/150x150/FF1493/ffffff?text=İ5',
                alt: 'İkramlik 5'
            },
            {
                id: '6',
                image: 'https://via.placeholder.com/150x150/FF1493/ffffff?text=İ6',
                alt: 'İkramlik 6'
            }
        ]
    }
];

const SolenHeroBanner: FC<SolenHeroBannerProps> = memo(
    ({categories = defaultCategories}) => {
        const [selectedCategory, setSelectedCategory] =
            useState<string>('biscolata');

        const currentCategory =
            categories.find(cat => cat.id === selectedCategory) ||
            categories[0];

        return (
            <div className="solen-hero-banner relative w-full overflow-hidden">
                {/* Background Image */}
                <div className="absolute inset-0">
                    <UiImage
                        src={currentCategory.backgroundImage}
                        alt={`${currentCategory.name} background`}
                        fill
                        className="object-cover transition-all duration-700"
                        priority
                    />
                    <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/50"></div>
                </div>

                {/* Content */}
                <div className="relative z-10 flex min-h-[600px] flex-col justify-center"></div>
            </div>
        );
    }
);

if (isDev) {
    SolenHeroBanner.displayName = 'SolenHeroBanner';
}

export default SolenHeroBanner;
