import {FC, memo, PropsWithChildren} from 'react';
import {isDev} from '@core/helpers';
import {LayoutProvider} from './context';
import FooterPartial from './partials/Footer';
import HeaderPartial from './partials/Header';
import NavBarPartial from './partials/NavBar';
import TopBarPartial from './partials/TopBar';

type DefaultLayoutProps = {
    pageProps: Record<string, any>;
};

const DefaultLayout: FC<PropsWithChildren<DefaultLayoutProps>> = memo(
    ({pageProps, children}) => {
        return (
            <LayoutProvider>
                <div className="relative w-full bg-white xl:flex xl:min-h-screen xl:flex-col xl:flex-nowrap">
                    <main className="xl:flex-1">{children}</main>

                    <FooterPartial />
                </div>
            </LayoutProvider>
        );
    }
);

if (isDev) {
    DefaultLayout.displayName = 'DefaultLayout';
}

export default DefaultLayout;
