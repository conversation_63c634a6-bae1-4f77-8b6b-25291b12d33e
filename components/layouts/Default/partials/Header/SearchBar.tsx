import {
    ChangeEventHandler,
    FC,
    FocusEventHandler,
    Fragment,
    KeyboardEventHandler,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import storeConfig from '~/store.config';
import {
    UiButton,
    UiImage,
    UiInput,
    UiLink,
    UiSpinner,
    UiTransition
} from '@core/components/ui';
import {cls, debounce, isDev, jsonRequest, toUpper, trim} from '@core/helpers';
import {useClickOutside, useElementSize, useTrans} from '@core/hooks';
import {SearchIcon} from '@core/icons/outline';
import {ProductSearchResultItem} from '@core/types';
import EmptyResult from '@components/common/EmptyResult';

const SearchResultItem: FC<{
    item: ProductSearchResultItem;
    onClick: () => void;
}> = memo(({item, onClick}) => {
    const t = useTrans();
    const description = useMemo(() => {
        if (item.type === 'product') {
            return t('Product');
        } else if (item.type === 'brand') {
            return t('Brand');
        } else if (item.type === 'navigation') {
            return t('Category');
        }

        return '';
    }, [t, item.type]);

    return (
        <UiLink
            className="group flex flex-shrink-0 cursor-pointer rounded border-0 p-2 transition hover:bg-gray-100 focus:outline-none"
            href={`/${item.slug}`}
            onClick={onClick}
        >
            {typeof item.image === 'string' && item.image.length > 0 ? (
                <div
                    className={cls(
                        'h-12 flex-shrink-0 overflow-hidden rounded',
                        {
                            'w-9':
                                storeConfig.catalog.productImageShape ===
                                'rectangle',
                            'w-12':
                                storeConfig.catalog.productImageShape !==
                                'rectangle'
                        }
                    )}
                >
                    <UiImage
                        className="h-full w-full rounded"
                        src={
                            item.isAdultProduct
                                ? '/adult-image.png'
                                : item.image
                        }
                        alt={item.name}
                        width={
                            storeConfig.catalog.productImageShape ===
                            'rectangle'
                                ? 36
                                : 48
                        }
                        height={48}
                        fit="cover"
                        position="center"
                        quality={75}
                    />
                </div>
            ) : (
                <div className="flex h-12 w-12 items-center justify-center bg-primary-50 text-xl font-semibold text-primary-600 transition group-hover:bg-primary-100">
                    {item.name
                        .split(' ')
                        .slice(0, 2)
                        .map(s => toUpper(s)[0])
                        .join('')}
                </div>
            )}

            <div className="ml-3 flex flex-1 flex-col justify-center">
                <div className="mb-1 text-sm font-medium">{item.name}</div>
                <div className="text-sm text-muted">{description}</div>
            </div>
        </UiLink>
    );
});

if (isDev) {
    SearchResultItem.displayName = 'SearchResultItem';
}

const HeaderSearchBarPartial: FC = memo(() => {
    const router = useRouter();
    const t = useTrans();
    const [searchResult, setSearchResult] = useState<ProductSearchResultItem[]>(
        []
    );
    const [searchQuery, setSearchQuery] = useState('');
    const [isResultShown, setIsResultShown] = useState(false);
    const [isBlurInProgress, setIsBlurInProgress] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isSearchPageLoading, setIsSearchPageLoading] = useState(false);
    const {ref: containerRef, width: containerWidth} = useElementSize();
    const blurTimeoutIdx = useRef<any>();
    const inputRef = useRef<HTMLInputElement>(null);

    const searchDebounced = useRef(
        debounce(
            async (query: string) => {
                query = trim(query);

                try {
                    if (!(query === '' || query.length < 2)) {
                        const result = await jsonRequest({
                            url: '/api/catalog/search',
                            method: 'POST',
                            data: {searchQuery: query}
                        });

                        setSearchResult(() =>
                            result.map((item: ProductSearchResultItem) => ({
                                ...item,
                                name: !!item.brandName
                                    ? `${item.brandName} ${item.name}`
                                    : item.name
                            }))
                        );
                    } else {
                        setSearchResult([]);
                    }
                } catch (error: any) {}

                setIsLoading(false);
            },
            250,
            {leading: false, trailing: true}
        )
    );
    const onSearch: ChangeEventHandler<HTMLInputElement> = useCallback(e => {
        const query = e.target.value;

        setIsLoading(true);
        searchDebounced.current(query);
        setSearchQuery(query);
    }, []);
    const onFocus: FocusEventHandler<HTMLInputElement> = useCallback(e => {
        setIsBlurInProgress(false);
        setIsResultShown(true);

        try {
            clearTimeout(blurTimeoutIdx.current);
        } catch (error: any) {}
    }, []);
    const onBlur: FocusEventHandler<HTMLInputElement> = useCallback(e => {
        setIsBlurInProgress(true);
    }, []);
    useEffect(() => {
        if (isBlurInProgress) {
            blurTimeoutIdx.current = setTimeout(() => {
                setIsBlurInProgress(false);
            }, 200);
        }

        return () => {
            try {
                clearTimeout(blurTimeoutIdx.current);
            } catch (error: any) {}
        };
    }, [isBlurInProgress]);

    const onPopularSearchClick = useCallback((query: string) => {
        setIsLoading(true);

        query = trim(query);

        (async () => {
            try {
                const result = await jsonRequest({
                    url: '/api/catalog/search',
                    method: 'POST',
                    data: {searchQuery: query}
                });

                setSearchResult(() =>
                    result.map((item: ProductSearchResultItem) => ({
                        ...item,
                        name: !!item.brandName
                            ? `${item.brandName} ${item.name}`
                            : item.name
                    }))
                );
            } catch (error: any) {}

            setIsLoading(false);
            setSearchQuery(query);
        })();
    }, []);
    const onPopularCategoryClick = useCallback(() => {
        setIsResultShown(false);
        setSearchQuery('');
    }, []);
    const onSearchItemClick = useCallback(() => {
        (async () => {
            try {
                if (!(searchQuery === '' || searchQuery.length < 2)) {
                    await jsonRequest({
                        url: '/api/catalog/save-popular-search',
                        method: 'POST',
                        data: {searchQuery: searchQuery}
                    });
                }
            } catch (error: any) {}
        })();

        setIsResultShown(false);
        setSearchResult([]);
        setIsLoading(false);
        setSearchQuery('');
        setIsBlurInProgress(false);
        try {
            clearTimeout(blurTimeoutIdx.current);
        } catch (error: any) {}
        if (!!inputRef.current) {
            inputRef.current.blur();
        }
    }, [searchQuery]);

    const onGoToSearchDetail = useCallback(async () => {
        setIsSearchPageLoading(true);
        setIsResultShown(false);
        setSearchResult([]);
        setIsLoading(false);
        setSearchQuery('');
        setIsBlurInProgress(false);
        try {
            clearTimeout(blurTimeoutIdx.current);
        } catch (error: any) {}

        await router.push(
            `/search?${new URLSearchParams({query: searchQuery}).toString()}`
        );

        if (!!inputRef.current) {
            inputRef.current.blur();
        }
        setIsSearchPageLoading(false);
    }, [router, searchQuery]);
    const onKeyDown: KeyboardEventHandler<HTMLInputElement> = useCallback(
        e => {
            if (e.code === 'Enter' || e.code === 'NumpadAdd') {
                onGoToSearchDetail();
            }
        },
        [onGoToSearchDetail]
    );

    const clickOutsideRef = useClickOutside(() => setIsResultShown(false));

    return (
        <>
            <div className="flex flex-1 items-stretch justify-center px-2">
                <div
                    ref={containerRef}
                    className="flex w-full max-w-3xl items-center"
                >
                    <div
                        ref={clickOutsideRef}
                        className={cls('relative w-full', {
                            'z-dropdown': isResultShown || isBlurInProgress
                        })}
                    >
                        <UiInput.Group size="xl" className="w-full">
                            <UiInput.LeftElement>
                                <div className="rounded-full border border-gray-200 bg-white p-2.5">
                                    <SearchIcon className="h-4 w-4 text-primary-600" />
                                </div>
                            </UiInput.LeftElement>

                            <UiInput
                                placeholder={t(
                                    'Search product, category or brand..'
                                )}
                                className={cls(
                                    'rounded-3xl border-gray-100 bg-gray-100 pl-14 transition focus:!border-gray-200 focus:bg-white focus:ring-0',
                                    {
                                        '!rounded-b-none !border-gray-200':
                                            isResultShown || isBlurInProgress
                                    }
                                )}
                                value={searchQuery}
                                onChange={onSearch}
                                onFocus={onFocus}
                                onBlur={onBlur}
                                onKeyDown={onKeyDown}
                            />

                            {isLoading && (
                                <UiInput.RightElement>
                                    <UiSpinner size="sm" />
                                </UiInput.RightElement>
                            )}
                        </UiInput.Group>

                        <UiTransition
                            as={Fragment}
                            enter="transition duration-300"
                            enterFrom="opacity-0"
                            enterTo="opacity-100"
                            leave="transition duration-200"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                            show={isResultShown}
                        >
                            <div
                                className="absolute left-0 right-0 top-full z-dropdown flex origin-top flex-col rounded-b border border-t-0 border-gray-200 bg-white shadow-sm outline-none"
                                style={{width: `${containerWidth}px`}}
                            >
                                {searchResult.length > 0 ? (
                                    <>
                                        <div className="scroller max-h-96 w-full overflow-y-auto p-2">
                                            {searchResult.map(item => (
                                                <SearchResultItem
                                                    key={item.id}
                                                    item={item}
                                                    onClick={onSearchItemClick}
                                                />
                                            ))}
                                        </div>
                                        <div className="border-t border-gray-200 p-3">
                                            <UiButton
                                                variant="outline"
                                                color="primary"
                                                size="sm"
                                                className="w-full"
                                                onClick={onGoToSearchDetail}
                                            >
                                                {t(
                                                    'SHOW ALL THE SEARCH RESULTS'
                                                )}
                                            </UiButton>
                                        </div>
                                    </>
                                ) : !isLoading && searchQuery.length >= 2 ? (
                                    <div className="flex w-full flex-col items-center justify-center p-10">
                                        <div className="flex h-12 w-12 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500">
                                            <SearchIcon className="h-4 w-4" />
                                        </div>

                                        <h2 className="pt-5 text-center font-semibold text-base">
                                            {t('No result found!')}
                                        </h2>

                                        <p className="pt-2.5 text-center text-muted">
                                            {t(
                                                'Please change your search criteria and try again.'
                                            )}
                                        </p>
                                    </div>
                                ) : (
                                    <EmptyResult
                                        onPopularSearchClick={
                                            onPopularSearchClick
                                        }
                                        onPopularCategoryClick={
                                            onPopularCategoryClick
                                        }
                                    />
                                )}
                            </div>
                        </UiTransition>
                    </div>
                </div>
            </div>

            <UiTransition
                show={isResultShown}
                as={Fragment}
                enter="transition duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="transition duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
            >
                <div className="fixed inset-0 z-20 bg-gray-900 bg-opacity-20 transition-opacity" />
            </UiTransition>

            <UiTransition
                show={isSearchPageLoading}
                as={Fragment}
                enter="transition duration-75"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="transition duration-75"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
            >
                <div className="fixed inset-0 z-dropdown bg-white bg-opacity-40 backdrop-blur-sm" />
            </UiTransition>
        </>
    );
});

if (isDev) {
    HeaderSearchBarPartial.displayName = 'HeaderSearchBarPartial';
}

export default HeaderSearchBarPartial;
