import {FC, Fragment, memo, useCallback, useState} from 'react';
import dynamic from 'next/dynamic';
import {useRouter} from 'next/router';
import {signOut} from 'next-auth/react';
import {Cookies} from 'react-cookie-consent';
import {isDev} from '@core/helpers';
import {useCart, useCustomer, useTrans, useUI} from '@core/hooks';
import {
    UiAvatar,
    UiDivider,
    UiLink,
    UiMenu,
    UiTransition
} from '@core/components/ui';
import {CartIcon, UserIcon} from '@core/icons/outline';
import {
    CartIcon as SolidCartIcon,
    UserIcon as SolidUserIcon
} from '@core/icons/solid';

const MiniCart = dynamic(() => import('@components/common/MiniCart'));

const HeaderUserNavPartial: FC = memo(() => {
    const [cartHover, setCartHover] = useState(false);
    const [userHover, setUserHover] = useState(false);
    const t = useTrans();
    const router = useRouter();
    const {openSideBar} = useUI();
    const customer = useCustomer();
    const {itemCount: cartItemsCount} = useCart();

    const onOpenMiniCart = useCallback(() => {
        openSideBar(t('My Cart'), <MiniCart />);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <nav className="flex w-60 items-center justify-end">
            {customer && (
                <UiMenu as="div" className="relative inline-block">
                    <UiMenu.Button
                        as="div"
                        aria-label={customer.name}
                        className="group relative flex cursor-pointer items-center px-2 py-2 text-gray-700 outline-none hover:text-primary-600 focus:outline-none active:outline-none"
                    >
                        <UiAvatar
                            className="cursor-pointer bg-gray-500 text-white group-hover:bg-primary-600"
                            name={customer.name}
                            size="md"
                        />

                        <div className="ml-2">{t('My Account')}</div>
                    </UiMenu.Button>

                    <UiTransition
                        as={Fragment}
                        enter="transition"
                        enterFrom="transform opacity-0"
                        enterTo="transform opacity-100"
                        leave="transition"
                        leaveFrom="transform opacity-100"
                        leaveTo="transform opacity-0"
                    >
                        <UiMenu.Items
                            className="absolute right-0 z-dropdown mt-2 min-w-max origin-top-right rounded border border-gray-200 bg-white shadow-sm outline-none"
                            style={{minWidth: '14rem'}}
                        >
                            <div className="px-1 py-1">
                                <div role="group">
                                    <div className="mx-3 my-2 text-sm ">
                                        <div className="font-medium">
                                            {customer.name}
                                        </div>
                                        <div className="text-muted">
                                            {customer.email}
                                        </div>
                                    </div>

                                    <UiDivider
                                        orientation="horizontal"
                                        className="border-gray-200"
                                    />

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-orders"
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                        >
                                            {t('My Orders')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-favorites"
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                        >
                                            {t('My Favorites')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-collections"
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                        >
                                            {t('My Collections')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-reviews"
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                        >
                                            {t('My Reviews')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-addresses"
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                        >
                                            {t('My Addresses')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-account"
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                        >
                                            {t('My Account')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <button
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                            onClick={() => {
                                                Cookies.remove('cart-id');
                                                signOut();
                                            }}
                                        >
                                            {t('Sign Out')}
                                        </button>
                                    </UiMenu.Item>
                                </div>
                            </div>
                        </UiMenu.Items>
                    </UiTransition>
                </UiMenu>
            )}
            {!customer && (
                <button
                    onClick={() =>
                        router.push(`/auth?redirect=${router.asPath}`)
                    }
                    className="relative flex cursor-pointer items-center rounded px-2 py-2 text-gray-700 outline-none hover:text-primary-600 focus:outline-none active:outline-none"
                    onMouseEnter={() => setUserHover(true)}
                    onMouseLeave={() => setUserHover(false)}
                >
                    {userHover ? (
                        <SolidUserIcon className="h-[1.1rem] w-[1.1rem]" />
                    ) : (
                        <UserIcon className="h-[1.1rem] w-[1.1rem]" />
                    )}

                    <div className="ml-2">{t('Sign In')}</div>
                </button>
            )}

            <button
                className="relative flex cursor-pointer items-center rounded px-2 py-2 text-gray-700 outline-none hover:text-primary-600 focus:outline-none active:outline-none"
                onClick={onOpenMiniCart}
                onMouseEnter={() => setCartHover(true)}
                onMouseLeave={() => setCartHover(false)}
            >
                {cartHover ? (
                    <SolidCartIcon className="h-5 w-5" />
                ) : (
                    <CartIcon className="h-5 w-5" />
                )}

                <div className="ml-2">{t('My Cart')}</div>

                {cartItemsCount > 0 && (
                    <span
                        className="absolute -top-1 left-5 flex items-center justify-center rounded-full border border-gray-100 bg-primary-600 text-[10px] font-medium text-white"
                        style={{
                            paddingLeft: '2.5px',
                            paddingRight: '2.5px',
                            minWidth: '1.25rem',
                            minHeight: '1.25rem'
                        }}
                    >
                        {cartItemsCount}
                    </span>
                )}
            </button>
        </nav>
    );
});

if (isDev) {
    HeaderUserNavPartial.displayName = 'HeaderUserNavPartial';
}

export default HeaderUserNavPartial;
