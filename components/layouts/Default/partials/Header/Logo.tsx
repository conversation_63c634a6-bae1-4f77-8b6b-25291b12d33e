import {FC, memo} from 'react';
import siteLogo from '@assets/images/common/site-logo.png';
import storeConfig from '~/store.config';
import {isDev} from '@core/helpers';
import {UiImage, UiLink} from '@core/components/ui';

const HeaderLogoPartial: FC = memo(() => {
    return (
        <div className="flex w-60 items-center">
            <UiLink
                className="h-logo cursor-pointer"
                href="/"
                aria-label="Logo"
            >
                <UiImage
                    src={siteLogo}
                    alt={storeConfig.title}
                    width={parseFloat(
                        storeConfig.theme.logoWidth.replace('px', '')
                    )}
                    priority={true}
                />
            </UiLink>
        </div>
    );
});

if (isDev) {
    HeaderLogoPartial.displayName = 'HeaderLogoPartial';
}

export default HeaderLogoPartial;
