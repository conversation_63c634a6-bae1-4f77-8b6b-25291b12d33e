import {FC, memo} from 'react';
import storeConfig from '~/store.config';
import {UiLink} from '@core/components/ui';
import {cls, isDev} from '@core/helpers';
import {LinkType} from './types';

type NavBarMainLinksPartialProps = {
    activeLink?: LinkType;
    links: LinkType[];
    onMouseEnter: (link: LinkType) => void;
};

const NavBarMainLinksPartial: FC<NavBarMainLinksPartialProps> = memo(props => {
    const {activeLink, links, onMouseEnter} = props;

    return (
        <div className="flex flex-nowrap items-center justify-center">
            {links.map(link => (
                <div
                    className={cls(
                        'border-b-2 border-transparent text-center hover:border-primary-600 hover:text-primary-600',
                        {
                            'border-primary-600 text-primary-600':
                                activeLink?.id === link.id
                        }
                    )}
                    key={link.id}
                >
                    <UiLink
                        href={link.href}
                        className="flex items-center px-5 py-[23px] text-sm font-semibold"
                        style={{
                            height: `calc(${storeConfig.theme.navBarHeight} - 1rem)`
                        }}
                        onMouseEnter={() => onMouseEnter(link)}
                    >
                        {link.title}
                    </UiLink>
                </div>
            ))}
        </div>
    );
});

if (isDev) {
    NavBarMainLinksPartial.displayName = 'NavBarMainLinksPartial';
}

export default NavBarMainLinksPartial;
