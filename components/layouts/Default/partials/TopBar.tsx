import {FC, memo} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiLink} from '@core/components/ui';
import useLayout from '../useLayout';
import {useStore} from '@core/hooks';

const TopBarPartial: FC = memo(() => {
    const {isMegaMenuOpened, isMegaMenuCloseInProgress} = useLayout();
    const {navigation} = useStore();

    const catalogFile = navigation?.filter(
        nav => nav.type === 'file-catalog'
    )?.[0];

    return (
        <div
            className={cls(
                'hidden h-top-bar w-full flex-nowrap items-center justify-center bg-white text-sm xl:flex',
                {
                    'z-dropdown': isMegaMenuOpened || isMegaMenuCloseInProgress
                }
            )}
        >
            <UiLink
                className="px-3 text-gray-500 transition duration-100 ease-in-out hover:text-gray-700"
                href="/account/my-orders"
            >
                <PERSON><PERSON><PERSON>şlerim
            </UiLink>
            <UiLink
                className="px-3 text-gray-500 transition duration-150 ease-in-out hover:text-gray-700"
                href="/"
            >
                Kampanyalar
            </UiLink>
            <UiLink
                className="px-3 text-gray-500 transition duration-150 ease-in-out hover:text-gray-700"
                href="/"
            >
                {"Enterstore'da Satıcı Ol"}
            </UiLink>
            <UiLink
                className="px-3 text-gray-500 transition duration-150 ease-in-out hover:text-gray-700"
                href="/cozum-merkezi/sikca-sorulan-sorular"
            >
                Yardım & Destek
            </UiLink>

            {catalogFile && (
                <a
                    href={catalogFile.attachments?.[0].url}
                    download
                    target="_blank"
                    className="px-3 font-normal text-gray-500 transition duration-150 ease-in-out hover:text-gray-700"
                >
                    Katalog
                </a>
            )}
        </div>
    );
});

if (isDev) {
    TopBarPartial.displayName = 'TopBarPartial';
}

export default TopBarPartial;
