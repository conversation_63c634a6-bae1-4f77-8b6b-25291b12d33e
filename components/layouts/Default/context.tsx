import {
    createContext,
    FC,
    memo,
    PropsWithChildren,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {isDev} from '@core/helpers';

type LayoutContextType = {
    isMegaMenuOpened: boolean;
    isMegaMenuCloseInProgress: boolean;
    setIsMegaMenuOpened: (isScrolling: boolean) => void;
};

export const LayoutContext = createContext<LayoutContextType>(null as any);

export const LayoutProvider: FC<PropsWithChildren<unknown>> = memo(props => {
    const [isMegaMenuOpened, setIsMegaMenuOpened] = useState(false);
    const [isMegaMenuCloseInProgress, setIsMegaMenuCloseInProgress] =
        useState(false);
    const megaMenuTimoutIdx = useRef<any>();

    useEffect(() => {
        if (isMegaMenuCloseInProgress) {
            megaMenuTimoutIdx.current = setTimeout(() => {
                setIsMegaMenuCloseInProgress(false);
            }, 300);
        }

        return () => {
            try {
                clearTimeout(megaMenuTimoutIdx.current);
            } catch (error: any) {}
        };
    }, [isMegaMenuCloseInProgress]);
    useEffect(() => {
        return () => {
            try {
                clearTimeout(megaMenuTimoutIdx.current);

                setIsMegaMenuOpened(false);
                setIsMegaMenuCloseInProgress(false);
            } catch (error: any) {}
        };
    }, []);

    const setIsMegaMenuOpenedCallback = useCallback((isOpened: boolean) => {
        setIsMegaMenuOpened(isOpened);

        if (!isOpened) {
            setIsMegaMenuCloseInProgress(true);
        } else {
            setIsMegaMenuCloseInProgress(false);
            try {
                clearTimeout(megaMenuTimoutIdx.current);
            } catch (error: any) {}
        }
    }, []);

    const value: any = useMemo(
        () => ({
            isMegaMenuOpened,
            isMegaMenuCloseInProgress,

            setIsMegaMenuOpened: setIsMegaMenuOpenedCallback
        }),
        [
            isMegaMenuOpened,
            isMegaMenuCloseInProgress,
            setIsMegaMenuOpenedCallback
        ]
    );

    return <LayoutContext.Provider value={value} {...props} />;
});

if (isDev) {
    LayoutProvider.displayName = 'LayoutProvider';
}
